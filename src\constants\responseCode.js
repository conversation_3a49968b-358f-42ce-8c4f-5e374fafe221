/**
 * API响应码常量定义
 * 统一管理所有API接口的响应码
 */

// 成功响应码
export const SUCCESS_CODE = 200;

// 错误响应码
export const ERROR_CODE = -1;

// 业务错误码
export const BUSINESS_ERROR_CODE = 1;

// 权限错误码
export const PERMISSION_ERROR_CODE = 401;

// 未找到错误码
export const NOT_FOUND_ERROR_CODE = 404;

// 服务器错误码
export const SERVER_ERROR_CODE = 500;

// 响应码映射对象
export const RESPONSE_CODE_MAP = {
  [SUCCESS_CODE]: '操作成功',
  [ERROR_CODE]: '操作失败',
  [BUSINESS_ERROR_CODE]: '业务错误',
  [PERMISSION_ERROR_CODE]: '权限不足',
  [NOT_FOUND_ERROR_CODE]: '资源未找到',
  [SERVER_ERROR_CODE]: '服务器内部错误'
};

// 判断响应是否成功的工具函数
export const isSuccess = (code) => {
  return code === SUCCESS_CODE;
};

// 获取响应码对应的消息
export const getResponseMessage = (code) => {
  return RESPONSE_CODE_MAP[code] || '未知错误';
};

// 默认导出常用的响应码
export default {
  SUCCESS_CODE,
  ERROR_CODE,
  BUSINESS_ERROR_CODE,
  PERMISSION_ERROR_CODE,
  NOT_FOUND_ERROR_CODE,
  SERVER_ERROR_CODE,
  isSuccess,
  getResponseMessage
};
