import * as THREE from 'three'

/**
 * 基于Three.js和高德地图GLCustomLayer的3D航点管理器
 */
export class ThreeWaypointManager {
  constructor(map) {
    this.map = map
    this.customCoords = map.customCoords
    this.scene = null
    this.camera = null
    this.renderer = null
    this.glLayer = null
    this.waypoints = new Map() // 存储航点数据
    this.waypointMeshes = new Map() // 存储Three.js网格对象
    this.isInitialized = false
    this.animationId = null

    // 检查必要的依赖
    if (!this.map || !this.customCoords) {
      throw new Error('地图或坐标转换工具未初始化')
    }
    
    // 3D航点样式配置
    this.config = {
      cubeSize: 60, // 正方体边长
      pillarHeight: 200,
      pillarRadius: 8,
      baseRadius: 30,
      colors: {
        cube: 0x52c41a, // 正方体颜色
        pillar: 0x389e0d,
        base: 0x7cb342,
        selected: 0xff4d4f,
        hover: 0xffa940,
        // 不同类型航点的颜色
        takeoff: 0x1890ff, // 起飞点 - 蓝色
        landing: 0xfa8c16, // 降落点 - 橙色
        photo: 0x722ed1, // 拍照点 - 紫色
        video: 0xf5222d, // 录像点 - 红色
        waypoint: 0x52c41a // 普通航点 - 绿色
      }
    }
    
    this.initGLLayer()
  }

  /**
   * 初始化GLCustomLayer
   */
  initGLLayer() {
    try {
      console.log('开始初始化GLCustomLayer')

      this.glLayer = new AMap.GLCustomLayer({
        zIndex: 100,
        init: (gl) => {
          console.log('GLCustomLayer init回调被调用')
          try {
            this.initThreeJS(gl)
            this.isInitialized = true
            console.log('Three.js初始化成功')
          } catch (error) {
            console.error('Three.js初始化失败:', error)
          }
        },
        render: () => {
          if (!this.isInitialized) return
          try {
            this.renderThreeJS()
          } catch (error) {
            console.error('Three.js渲染失败:', error)
          }
        }
      })

      this.map.add(this.glLayer)
      this.startAnimation()
      console.log('GLCustomLayer添加到地图成功')
    } catch (error) {
      console.error('GLCustomLayer初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化Three.js场景
   */
  initThreeJS(gl) {
    console.log('开始初始化Three.js场景')

    try {
      // 按照官方示例创建透视相机
      this.camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        100, // 官方示例使用100作为near值
        1 << 30
      )
      console.log('相机创建成功')

      // 创建渲染器，按照官方示例
      this.renderer = new THREE.WebGLRenderer({
        context: gl
        // 不设置alpha和antialias，按照官方示例
      })
      this.renderer.autoClear = false
      console.log('渲染器创建成功')

      // 创建场景
      this.scene = new THREE.Scene()
      console.log('场景创建成功')

      // 添加光照
      this.setupLighting()
      console.log('光照设置完成')

    } catch (error) {
      console.error('初始化Three.js场景失败:', error)
      throw error
    }
  }

  /**
   * 设置光照系统
   */
  setupLighting() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4)
    this.scene.add(ambientLight)

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(1000, 1000, 1000)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    directionalLight.shadow.camera.near = 0.5
    directionalLight.shadow.camera.far = 5000
    this.scene.add(directionalLight)

    // 补充光源
    const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.3)
    fillLight.position.set(-1000, -500, 500)
    this.scene.add(fillLight)
  }

  /**
   * 渲染Three.js场景
   */
  renderThreeJS() {
    if (!this.renderer || !this.scene || !this.camera || !this.customCoords) {
      return
    }

    try {
      // 按照官方示例的渲染流程
      this.renderer.resetState()

      // 重新设置图层的渲染中心点，按照官方示例
      this.customCoords.setCenter([116.52, 39.79])

      // 获取相机参数
      const cameraParams = this.customCoords.getCameraParams()
      if (!cameraParams) {
        console.warn('无法获取相机参数')
        return
      }

      const { near, far, fov, up, lookAt, position } = cameraParams

      // 检查参数有效性
      if (!position || !up || !lookAt) {
        console.warn('相机参数不完整')
        return
      }

      // 按照官方示例设置相机参数
      this.camera.near = near
      this.camera.far = far
      this.camera.fov = fov
      this.camera.position.set(...position)
      this.camera.up.set(...up)
      this.camera.lookAt(...lookAt)
      this.camera.updateProjectionMatrix()

      // 渲染场景
      this.renderer.render(this.scene, this.camera)
      this.renderer.resetState()
    } catch (error) {
      console.error('渲染Three.js场景时出错:', error)
    }
  }

  /**
   * 根据航点类型获取颜色
   */
  getWaypointColor(waypoint) {
    if (waypoint.selected) {
      return this.config.colors.selected
    }

    // 根据航点类型或动作确定颜色
    if (waypoint.type) {
      return this.config.colors[waypoint.type] || this.config.colors.waypoint
    }

    // 根据动作类型确定颜色
    if (waypoint.actions && waypoint.actions.length > 0) {
      const hasPhoto = waypoint.actions.some(action => action.type === 'takePhoto')
      const hasVideo = waypoint.actions.some(action => action.type === 'startRecord')

      if (hasVideo) return this.config.colors.video
      if (hasPhoto) return this.config.colors.photo
    }

    return this.config.colors.waypoint
  }

  /**
   * 添加3D航点
   */
  addWaypoint(id, longitude, latitude, height = 100, options = {}) {
    // 转换坐标
    const coords = this.customCoords.lngLatsToCoords([[longitude, latitude]])
    if (!coords || coords.length === 0 || !coords[0]) {
      console.error('坐标转换失败:', longitude, latitude)
      return null
    }
    const [x, y] = coords[0]

    // 存储航点数据
    const waypoint = {
      id,
      longitude,
      latitude,
      height: Math.max(height, 10), // 确保最小高度，避免贴地被裁剪
      x,
      y,
      selected: false,
      ...options
    }
    this.waypoints.set(id, waypoint)

    // 创建3D网格
    const mesh = this.create3DWaypointMesh(waypoint)
    this.waypointMeshes.set(id, mesh)
    this.scene.add(mesh)

    return waypoint
  }

  /**
   * 创建3D航点网格
   */
  create3DWaypointMesh(waypoint) {
    const group = new THREE.Group()
    group.userData = { waypointId: waypoint.id }

    // 按照官方示例，设置合适的高度，确保对象在地面上方
    const objectHeight = Math.max(waypoint.height, 500) // 设置最小500米高度，参考官方示例

    // 1. 创建正方体（航点主体）
    const cubeGeometry = new THREE.BoxGeometry(
      this.config.cubeSize,
      this.config.cubeSize,
      this.config.cubeSize
    )
    const cubeMaterial = new THREE.MeshPhongMaterial({
      color: this.getWaypointColor(waypoint),
      shininess: 100,
      depthTest: true,
      transparent: true
    })
    const cube = new THREE.Mesh(cubeGeometry, cubeMaterial)
    // 按照官方示例设置位置，确保在地面上方
    cube.position.set(0, 0, objectHeight + this.config.cubeSize / 2)
    cube.castShadow = true
    cube.receiveShadow = true
    group.add(cube)

    // 1.1 添加正方体边框
    const edgesGeometry = new THREE.EdgesGeometry(cubeGeometry)
    const edgesMaterial = new THREE.LineBasicMaterial({
      color: 0x000000,
      transparent: true,
      opacity: 0.6,
      linewidth: 2,
      depthTest: true
    })
    const edges = new THREE.LineSegments(edgesGeometry, edgesMaterial)
    edges.position.set(0, 0, safeHeight + this.config.cubeSize / 2 + 5)
    group.add(edges)

    // 2. 创建支柱
    const pillarHeight = objectHeight
    const pillarGeometry = new THREE.CylinderGeometry(
      this.config.pillarRadius,
      this.config.pillarRadius,
      pillarHeight,
      16
    )
    const pillarMaterial = new THREE.MeshPhongMaterial({
      color: this.config.colors.pillar,
      transparent: true,
      opacity: 0.8,
      depthTest: true
    })
    const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial)
    pillar.position.set(0, 0, pillarHeight / 2)
    pillar.castShadow = true
    pillar.receiveShadow = true
    group.add(pillar)

    // 3. 创建地面基座
    const baseGeometry = new THREE.CylinderGeometry(this.config.baseRadius, this.config.baseRadius, 5, 32)
    const baseMaterial = new THREE.MeshPhongMaterial({
      color: this.config.colors.base,
      transparent: true,
      opacity: 0.6,
      depthTest: true
    })
    const base = new THREE.Mesh(baseGeometry, baseMaterial)
    base.position.set(0, 0, 2.5)
    base.receiveShadow = true
    group.add(base)

    // 4. 添加高度标签（使用CSS2DRenderer或纹理）
    this.addHeightLabel(group, waypoint)

    // 设置位置
    group.position.set(waypoint.x, waypoint.y, 0)

    // 添加动画
    this.addWaypointAnimation(group, waypoint)

    return group
  }

  /**
   * 添加高度标签
   */
  addHeightLabel(group, waypoint) {
    // 创建文字纹理
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    canvas.width = 256
    canvas.height = 64
    
    context.fillStyle = 'rgba(0, 0, 0, 0.8)'
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    context.fillStyle = '#ffffff'
    context.font = 'bold 24px Arial'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText(`${waypoint.height}m`, canvas.width / 2, canvas.height / 2)
    
    const texture = new THREE.CanvasTexture(canvas)
    const labelMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1
    })
    
    const labelGeometry = new THREE.PlaneGeometry(100, 25)
    const label = new THREE.Mesh(labelGeometry, labelMaterial)
    label.position.set(0, 0, waypoint.height + this.config.cubeSize / 2 + 50)

    group.add(label)
  }

  /**
   * 添加航点动画
   */
  addWaypointAnimation(group, waypoint) {
    // 浮动动画
    group.userData.animationOffset = Math.random() * Math.PI * 2
    group.userData.originalZ = group.position.z

    // 为正方体添加旋转动画数据
    const cube = group.children.find(child => child.geometry instanceof THREE.BoxGeometry)
    if (cube) {
      cube.userData.rotationSpeed = {
        x: (Math.random() - 0.5) * 0.02,
        y: (Math.random() - 0.5) * 0.02,
        z: (Math.random() - 0.5) * 0.02
      }
    }
  }

  /**
   * 更新航点高度
   */
  updateWaypointHeight(id, newHeight) {
    const waypoint = this.waypoints.get(id)
    const mesh = this.waypointMeshes.get(id)

    if (!waypoint || !mesh) return

    waypoint.height = newHeight

    // 更新正方体位置
    const cube = mesh.children.find(child => child.geometry instanceof THREE.BoxGeometry)
    if (cube) {
      cube.position.z = newHeight + this.config.cubeSize / 2
    }

    // 更新正方体边框位置
    const edges = mesh.children.find(child => child instanceof THREE.LineSegments)
    if (edges) {
      edges.position.z = newHeight + this.config.cubeSize / 2
    }

    // 更新支柱
    const pillar = mesh.children.find(child => child.geometry instanceof THREE.CylinderGeometry)
    if (pillar && pillar.position.z > 10) { // 区分支柱和基座
      pillar.geometry.dispose()
      pillar.geometry = new THREE.CylinderGeometry(
        this.config.pillarRadius, 
        this.config.pillarRadius, 
        newHeight, 
        16
      )
      pillar.position.z = newHeight / 2
    }

    // 更新标签
    const label = mesh.children.find(child => child.geometry instanceof THREE.PlaneGeometry)
    if (label) {
      label.position.z = newHeight + this.config.cubeSize / 2 + 50
      
      // 更新标签文字
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 256
      canvas.height = 64
      
      context.fillStyle = 'rgba(0, 0, 0, 0.8)'
      context.fillRect(0, 0, canvas.width, canvas.height)
      
      context.fillStyle = '#ffffff'
      context.font = 'bold 24px Arial'
      context.textAlign = 'center'
      context.textBaseline = 'middle'
      context.fillText(`${newHeight}m`, canvas.width / 2, canvas.height / 2)
      
      label.material.map.dispose()
      label.material.map = new THREE.CanvasTexture(canvas)
      label.material.needsUpdate = true
    }
  }

  /**
   * 选中航点
   */
  selectWaypoint(id) {
    // 取消之前的选中状态
    this.waypoints.forEach((waypoint, waypointId) => {
      if (waypoint.selected) {
        waypoint.selected = false
        this.updateWaypointAppearance(waypointId)
      }
    })

    // 设置新的选中状态
    const waypoint = this.waypoints.get(id)
    if (waypoint) {
      waypoint.selected = true
      this.updateWaypointAppearance(id)
    }
  }

  /**
   * 更新航点外观
   */
  updateWaypointAppearance(id) {
    const waypoint = this.waypoints.get(id)
    const mesh = this.waypointMeshes.get(id)

    if (!waypoint || !mesh) return

    const cube = mesh.children.find(child => child.geometry instanceof THREE.BoxGeometry)
    if (cube) {
      cube.material.color.setHex(this.getWaypointColor(waypoint))
    }
  }

  /**
   * 更新航点动作
   */
  updateWaypointActions(id, actions) {
    const waypoint = this.waypoints.get(id)
    if (!waypoint) return

    waypoint.actions = actions
    this.updateWaypointAppearance(id)
  }

  /**
   * 更新航点类型
   */
  updateWaypointType(id, type) {
    const waypoint = this.waypoints.get(id)
    if (!waypoint) return

    waypoint.type = type
    this.updateWaypointAppearance(id)
  }

  /**
   * 删除航点
   */
  removeWaypoint(id) {
    const mesh = this.waypointMeshes.get(id)
    if (mesh) {
      this.scene.remove(mesh)
      // 清理几何体和材质
      mesh.traverse((child) => {
        if (child.geometry) child.geometry.dispose()
        if (child.material) {
          if (child.material.map) child.material.map.dispose()
          child.material.dispose()
        }
      })
    }
    
    this.waypoints.delete(id)
    this.waypointMeshes.delete(id)
  }

  /**
   * 开始动画循环
   */
  startAnimation() {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      this.updateAnimations()
      this.map.render()
    }
    animate()
  }

  /**
   * 更新动画
   */
  updateAnimations() {
    const time = Date.now() * 0.001

    this.waypointMeshes.forEach((mesh) => {
      if (mesh.userData.animationOffset !== undefined) {
        // 浮动动画
        const floatOffset = Math.sin(time * 2 + mesh.userData.animationOffset) * 10
        mesh.position.z = mesh.userData.originalZ + floatOffset

        // 正方体旋转动画
        const cube = mesh.children.find(child => child.geometry instanceof THREE.BoxGeometry)
        const edges = mesh.children.find(child => child instanceof THREE.LineSegments)
        if (cube && cube.userData.rotationSpeed) {
          cube.rotation.x += cube.userData.rotationSpeed.x
          cube.rotation.y += cube.userData.rotationSpeed.y
          cube.rotation.z += cube.userData.rotationSpeed.z

          // 边框跟随正方体旋转
          if (edges) {
            edges.rotation.x = cube.rotation.x
            edges.rotation.y = cube.rotation.y
            edges.rotation.z = cube.rotation.z
          }
        }
      }
    })
  }

  /**
   * 停止动画
   */
  stopAnimation() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.stopAnimation()
    
    // 清理所有航点
    this.waypoints.forEach((_, id) => {
      this.removeWaypoint(id)
    })
    
    // 移除图层
    if (this.glLayer) {
      this.map.remove(this.glLayer)
    }
    
    // 清理Three.js资源
    if (this.renderer) {
      this.renderer.dispose()
    }
  }

  /**
   * 获取所有航点
   */
  getAllWaypoints() {
    return Array.from(this.waypoints.values())
  }

  /**
   * 获取航点
   */
  getWaypoint(id) {
    return this.waypoints.get(id)
  }
}
