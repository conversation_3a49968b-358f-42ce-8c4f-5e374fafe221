<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备序列号" prop="device_sn">
        <el-input
          v-model="queryParams.device_sn"
          placeholder="请输入设备序列号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="设备昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入设备昵称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="设备类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择设备类型" clearable style="width: 200px">
          <el-option label="机场" :value="DEVICE_TYPE_DOCK" />
          <el-option label="无人机" :value="DEVICE_TYPE_DRONE" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="在线状态" prop="online_status">
        <el-select v-model="queryParams.online_status" placeholder="请选择在线状态" clearable style="width: 200px">
          <el-option label="在线" :value="1" />
          <el-option label="离线" :value="0" />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefresh"
        >刷新列表</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 设备列表 -->
    <el-table v-loading="loading" :data="deviceList" stripe>
      <el-table-column label="设备序列号" align="center" prop="device_sn" width="180" :show-overflow-tooltip="true" />
      <el-table-column label="设备名称" align="center" prop="device_name" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="设备昵称" align="center" prop="nickname" width="150" :show-overflow-tooltip="true" />
      
      <el-table-column label="设备类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === DEVICE_TYPE_DOCK" type="success">机场</el-tag>
          <el-tag v-else-if="scope.row.type === DEVICE_TYPE_DRONE" type="warning">无人机</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="在线状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.mode_code >= 0" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="固件版本" align="center" prop="firmware_version" width="120" :show-overflow-tooltip="true" />
      
      <el-table-column label="绑定状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.bound_status ? 'success' : 'danger'">
            {{ scope.row.bound_status ? '已绑定' : '未绑定' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="最后登录" align="center" prop="login_time" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.login_time || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.mode_code >= 0"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleViewLogs(scope.row)"
          >查看日志</el-button>
          <span v-else class="disabled-text">设备离线</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listDevice } from "@/api/device";
import { SUCCESS_CODE } from "@/constants/responseCode";
import { DEVICE_TYPE_DOCK, DEVICE_TYPE_DRONE, getDomainByDeviceType } from "@/constants/deviceTypes";

export default {
  name: "DeviceLogsList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        device_sn: null,
        nickname: null,
        type: null,
        online_status: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      // 构建查询参数，只获取机场和无人机设备
      const params = {
        ...this.queryParams,
        deviceType: null // 不限制设备类型，在前端过滤
      };
      
      listDevice(params).then(response => {
        if (response.code === SUCCESS_CODE) {
          // 过滤出机场和无人机设备
          const allDevices = response.data || [];
          this.deviceList = allDevices.filter(device =>
            device.type === DEVICE_TYPE_DOCK || device.type === DEVICE_TYPE_DRONE
          );
          
          // 如果有类型筛选，进一步过滤
          if (this.queryParams.type) {
            this.deviceList = this.deviceList.filter(device => 
              device.type === this.queryParams.type
            );
          }
          
          // 如果有在线状态筛选，进一步过滤
          if (this.queryParams.online_status !== null) {
            this.deviceList = this.deviceList.filter(device => {
              const isOnline = device.mode_code >= 0 ? 1 : 0;
              return isOnline === this.queryParams.online_status;
            });
          }
          
          this.total = this.deviceList.length;
        } else {
          this.deviceList = [];
          this.total = 0;
          this.$modal.msgError(response.message || "获取设备列表失败");
        }
        this.loading = false;
      }).catch(error => {
        this.deviceList = [];
        this.total = 0;
        this.loading = false;
        this.$modal.msgError("获取设备列表失败");
        console.error('获取设备列表出错:', error);
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 刷新按钮操作 */
    handleRefresh() {
      this.getList();
      this.$modal.msgSuccess("列表已刷新");
    },
    
    /** 查看日志按钮操作 */
    handleViewLogs(row) {
      // 判断设备类型，确定domain_list参数
      const domainList = getDomainByDeviceType(row.type);

      // 跳转到日志详情页面
      this.$router.push({
        path: '/device/logs/detail',
        query: {
          deviceSn: row.device_sn,
          domainList: domainList,
          deviceInfo: encodeURIComponent(JSON.stringify(row))
        }
      });
    }
  }
};
</script>

<style scoped>
.disabled-text {
  color: #c0c4cc;
  font-size: 12px;
}
</style>
