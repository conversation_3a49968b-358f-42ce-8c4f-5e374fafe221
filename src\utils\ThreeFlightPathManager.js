import * as THREE from 'three'

/**
 * 基于Three.js的3D航线路径管理器
 * 实现3D地形上的航线显示、飞行器动画等功能
 */
export class ThreeFlightPathManager {
  constructor(map) {
    this.map = map
    this.customCoords = map.customCoords
    this.scene = null
    this.camera = null
    this.renderer = null
    this.glLayer = null
    this.isInitialized = false
    this.animationId = null

    // 航线相关
    this.flightPaths = new Map() // 存储航线数据
    this.pathMeshes = new Map() // 存储航线网格对象
    this.aircrafts = new Map() // 存储飞行器对象
    this.waypoints = new Map() // 存储航点对象

    // 动画相关
    this.clock = new THREE.Clock()
    this.mixers = [] // 动画混合器

    // 固定渲染中心点 - 按照官方示例，必须保持固定
    this.fixedRenderCenter = [116.52, 39.79]

    // 预转换的坐标数据 - 按照官方示例，在GLCustomLayer创建前转换
    this.preConvertedData = new Map()

    // 检查必要的依赖
    if (!this.map || !this.customCoords) {
      throw new Error('地图或坐标转换工具未初始化')
    }
    
    // 3D航线样式配置
    this.config = {
      // 航线配置
      pathWidth: 8,
      pathHeight: 5,
      pathColor: 0x00ff00,
      pathOpacity: 0.8,
      
      // 航点配置
      waypointRadius: 30,
      waypointHeight: 100,
      waypointColor: 0xff4444,
      
      // 飞行器配置
      aircraftSize: 50,
      aircraftColor: 0x0088ff,
      aircraftSpeed: 50, // 米/秒
      
      // 地形配置
      terrainSegments: 128,
      terrainScale: 1000,
      terrainHeight: 200,
      
      // 动画配置
      animationSpeed: 1.0
    }
    
    this.initGLLayer()
  }

  /**
   * 初始化GLCustomLayer
   */
  initGLLayer() {
    try {
      console.log('开始初始化3D航线GLCustomLayer')

      this.glLayer = new AMap.GLCustomLayer({
        zIndex: 100,
        init: (gl) => {
          console.log('3D航线GLCustomLayer init回调被调用')
          try {
            this.initThreeJS(gl)
            this.isInitialized = true
            console.log('3D航线Three.js初始化成功')
          } catch (error) {
            console.error('3D航线Three.js初始化失败:', error)
          }
        },
        render: () => {
          if (!this.isInitialized) return
          try {
            this.renderThreeJS()
          } catch (error) {
            console.error('3D航线Three.js渲染失败:', error)
          }
        }
      })

      this.map.add(this.glLayer)
      this.startAnimation()
      console.log('3D航线GLCustomLayer添加到地图成功')
    } catch (error) {
      console.error('3D航线GLCustomLayer初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化Three.js场景 - 严格按照官方示例
   */
  initThreeJS(gl) {
    // 按照官方示例创建透视相机
    this.camera = new THREE.PerspectiveCamera(
      60,
      window.innerWidth / window.innerHeight,
      100,
      1 << 30
    )

    // 创建渲染器，按照官方示例
    this.renderer = new THREE.WebGLRenderer({
      context: gl // 地图的 gl 上下文
    })

    // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
    this.renderer.autoClear = false
    this.scene = new THREE.Scene()

    // 环境光照和平行光 - 按照官方示例
    const aLight = new THREE.AmbientLight(0xffffff, 0.3)
    const dLight = new THREE.DirectionalLight(0xffffff, 1)
    dLight.position.set(1000, -100, 900)
    this.scene.add(dLight)
    this.scene.add(aLight)

    // 使用预转换的数据创建3D对象 - 按照官方示例模式
    this.preConvertedData.forEach((data, pathId) => {
      this.createMeshesFromPreConvertedData(pathId, data)
    })

    console.log('3D航线Three.js场景初始化完成')
  }

  /**
   * 从预转换数据创建3D网格对象 - 按照官方示例模式
   */
  createMeshesFromPreConvertedData(pathId, data) {
    const { waypoints, convertedData, options } = data

    if (!convertedData || convertedData.length < 2) {
      console.error('预转换数据无效:', pathId)
      return
    }

    // 创建转换后的航点数据
    const convertedWaypoints = waypoints.map((wp, index) => {
      if (!convertedData[index]) {
        console.error('坐标转换数据缺失:', wp.longitude, wp.latitude)
        return null
      }
      const [x, y] = convertedData[index]
      return {
        ...wp,
        x: x,
        y: y,
        z: wp.height || 100
      }
    }).filter(wp => wp !== null)

    if (convertedWaypoints.length < 2) {
      console.error('有效航点数量不足:', pathId)
      return
    }

    // 创建3D路径网格
    const pathMesh = this.create3DPathMesh({
      id: pathId,
      waypoints: convertedWaypoints,
      ...options
    })
    this.pathMeshes.set(pathId, pathMesh)
    this.scene.add(pathMesh)

    // 创建航点标记
    this.createWaypointMarkers(pathId, convertedWaypoints)

    // 更新航线数据
    const flightPath = this.flightPaths.get(pathId)
    if (flightPath) {
      flightPath.convertedWaypoints = convertedWaypoints
      flightPath.totalDistance = this.calculatePathDistance(convertedWaypoints)
    }
  }

  /**
   * 渲染Three.js场景 - 完全按照官方示例
   */
  renderThreeJS() {
    // 这里必须执行！！重新设置 three 的 gl 上下文状态。
    this.renderer.resetState()

    // 重新设置图层的渲染中心点，将模型等物体的渲染中心点重置
    // 否则和 LOCA 可视化等多个图层能力使用的时候会出现物体位置偏移的问题
    this.customCoords.setCenter(this.fixedRenderCenter)

    const { near, far, fov, up, lookAt, position } = this.customCoords.getCameraParams()

    // 这里的顺序不能颠倒，否则可能会出现绘制卡顿的效果。
    this.camera.near = near
    this.camera.far = far
    this.camera.fov = fov
    this.camera.position.set(...position)
    this.camera.up.set(...up)
    this.camera.lookAt(...lookAt)
    this.camera.updateProjectionMatrix()

    // 更新动画（如果有的话）
    this.updateAnimations()

    this.renderer.render(this.scene, this.camera)

    // 这里必须执行！！重新设置 three 的 gl 上下文状态。
    this.renderer.resetState()
  }

  /**
   * 创建3D航线路径 - 严格按照官方示例模式
   */
  createFlightPath(pathId, waypoints, options = {}) {
    if (waypoints.length < 2) {
      console.warn('航线至少需要2个航点')
      return null
    }

    // 如果是第一个航线，根据航点位置设置渲染中心点
    if (this.flightPaths.size === 0) {
      // 计算航点的中心位置作为渲染中心点
      let sumLng = 0, sumLat = 0
      waypoints.forEach(wp => {
        sumLng += wp.longitude
        sumLat += wp.latitude
      })
      const centerLng = sumLng / waypoints.length
      const centerLat = sumLat / waypoints.length
      this.fixedRenderCenter = [centerLng, centerLat]
      console.log('设置渲染中心点:', this.fixedRenderCenter)
    }

    // 按照官方示例：数据使用转换工具进行转换，这个操作必须要提前执行
    // 重要：必须在GLCustomLayer创建之前完成坐标转换
    this.customCoords.setCenter(this.fixedRenderCenter)
    const lngLatArray = waypoints.map(wp => [wp.longitude, wp.latitude])
    const convertedData = this.customCoords.lngLatsToCoords(lngLatArray)

    // 存储预转换的坐标数据
    this.preConvertedData.set(pathId, {
      waypoints: waypoints,
      convertedData: convertedData,
      options: options
    })

    // 存储航线数据（原始格式，用于后续处理）
    const flightPath = {
      id: pathId,
      waypoints: waypoints, // 保存原始坐标用于显示
      ...options
    }
    this.flightPaths.set(pathId, flightPath)

    // 重新初始化GLCustomLayer以应用新的坐标数据
    this.reinitializeGLLayer()

    console.log('3D航线路径创建成功:', pathId)
    return flightPath
  }

  /**
   * 重新初始化GLCustomLayer
   */
  reinitializeGLLayer() {
    // 移除现有的图层
    if (this.glLayer) {
      this.map.remove(this.glLayer)
      this.stopAnimation()
    }

    // 重新创建图层
    this.initGLLayer()
  }

  /**
   * 创建3D路径网格
   */
  create3DPathMesh(flightPath) {
    const group = new THREE.Group()
    group.userData = { pathId: flightPath.id }

    const waypoints = flightPath.waypoints

    // 创建路径曲线
    const points = waypoints.map(wp => new THREE.Vector3(wp.x, wp.y, wp.z))
    const curve = new THREE.CatmullRomCurve3(points)

    // 创建管道几何体（3D路径）
    const tubeGeometry = new THREE.TubeGeometry(
      curve,
      Math.max(64, waypoints.length * 8), // 分段数
      this.config.pathWidth,
      8, // 径向分段
      false
    )

    // 创建路径材质
    const pathMaterial = new THREE.MeshPhongMaterial({
      color: this.config.pathColor,
      transparent: true,
      opacity: this.config.pathOpacity,
      shininess: 100
    })

    // 创建路径网格
    const pathMesh = new THREE.Mesh(tubeGeometry, pathMaterial)
    pathMesh.castShadow = true
    pathMesh.receiveShadow = true
    group.add(pathMesh)

    // 添加路径发光效果
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: this.config.pathColor,
      transparent: true,
      opacity: 0.3
    })
    const glowGeometry = new THREE.TubeGeometry(
      curve,
      Math.max(64, waypoints.length * 8),
      this.config.pathWidth * 1.5,
      8,
      false
    )
    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial)
    group.add(glowMesh)

    return group
  }

  /**
   * 创建航点标记
   */
  createWaypointMarkers(pathId, waypoints) {
    const waypointGroup = new THREE.Group()
    waypointGroup.userData = { pathId: pathId, type: 'waypoints' }

    waypoints.forEach((waypoint, index) => {
      // 创建航点几何体
      const geometry = new THREE.SphereGeometry(this.config.waypointRadius, 16, 16)
      const material = new THREE.MeshPhongMaterial({
        color: this.config.waypointColor,
        transparent: true,
        opacity: 0.8
      })

      const mesh = new THREE.Mesh(geometry, material)
      mesh.position.set(waypoint.x, waypoint.y, waypoint.z)
      mesh.castShadow = true
      mesh.receiveShadow = true
      mesh.userData = {
        waypointId: `${pathId}_waypoint_${index}`,
        pathId: pathId,
        index: index
      }

      waypointGroup.add(mesh)

      // 添加航点标签
      this.addWaypointLabel(mesh, `P${index + 1}`)
    })

    this.waypoints.set(pathId, waypointGroup)
    this.scene.add(waypointGroup)
  }



  /**
   * 添加航点标签
   */
  addWaypointLabel(mesh, text) {
    // 创建文字纹理
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    canvas.width = 128
    canvas.height = 64

    context.fillStyle = 'rgba(0, 0, 0, 0.8)'
    context.fillRect(0, 0, canvas.width, canvas.height)

    context.fillStyle = '#ffffff'
    context.font = 'bold 24px Arial'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText(text, canvas.width / 2, canvas.height / 2)

    // 创建标签
    const texture = new THREE.CanvasTexture(canvas)
    const labelMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1
    })

    const labelGeometry = new THREE.PlaneGeometry(80, 40)
    const labelMesh = new THREE.Mesh(labelGeometry, labelMaterial)
    labelMesh.position.set(0, 0, this.config.waypointRadius + 20)

    mesh.add(labelMesh)
  }

  /**
   * 计算路径总距离
   */
  calculatePathDistance(waypoints) {
    let totalDistance = 0
    for (let i = 1; i < waypoints.length; i++) {
      const prev = waypoints[i - 1]
      const curr = waypoints[i]
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + 
        Math.pow(curr.y - prev.y, 2) + 
        Math.pow(curr.z - prev.z, 2)
      )
      totalDistance += distance
    }
    return totalDistance
  }

  /**
   * 创建飞行器
   */
  createAircraft(aircraftId, pathId, options = {}) {
    const flightPath = this.flightPaths.get(pathId)
    if (!flightPath || !flightPath.convertedWaypoints) {
      console.error('航线不存在或未转换:', pathId)
      return null
    }

    // 创建飞行器几何体（简单的箭头形状）
    const aircraftGeometry = new THREE.ConeGeometry(
      this.config.aircraftSize * 0.5,
      this.config.aircraftSize * 2,
      8
    )

    const aircraftMaterial = new THREE.MeshPhongMaterial({
      color: this.config.aircraftColor,
      shininess: 100
    })

    const aircraftMesh = new THREE.Mesh(aircraftGeometry, aircraftMaterial)
    aircraftMesh.castShadow = true
    aircraftMesh.userData = {
      aircraftId: aircraftId,
      pathId: pathId,
      progress: 0, // 航线进度 0-1
      speed: options.speed || this.config.aircraftSpeed
    }

    // 设置初始位置为第一个转换后的航点
    const startWaypoint = flightPath.convertedWaypoints[0]
    aircraftMesh.position.set(startWaypoint.x, startWaypoint.y, startWaypoint.z)

    this.aircrafts.set(aircraftId, aircraftMesh)
    this.scene.add(aircraftMesh)

    console.log('飞行器创建成功:', aircraftId)
    return aircraftMesh
  }

  /**
   * 更新动画
   */
  updateAnimations() {
    const deltaTime = this.clock.getDelta()
    
    // 更新飞行器动画
    this.aircrafts.forEach((aircraft) => {
      this.updateAircraftAnimation(aircraft, deltaTime)
    })
    
    // 更新其他动画
    this.mixers.forEach(mixer => {
      mixer.update(deltaTime)
    })
  }

  /**
   * 更新飞行器动画
   */
  updateAircraftAnimation(aircraft, deltaTime) {
    const pathId = aircraft.userData.pathId
    const flightPath = this.flightPaths.get(pathId)
    if (!flightPath || !flightPath.convertedWaypoints) return

    const speed = aircraft.userData.speed
    const progress = aircraft.userData.progress

    // 使用转换后的固定坐标
    const waypoints = flightPath.convertedWaypoints
    if (waypoints.length < 2) return

    // 计算新的进度
    const distancePerSecond = speed
    const progressIncrement = (distancePerSecond * deltaTime) / flightPath.totalDistance
    const newProgress = Math.min(progress + progressIncrement, 1.0)

    aircraft.userData.progress = newProgress

    // 根据进度计算位置
    const position = this.getPositionOnPath(waypoints, newProgress)
    if (position) {
      aircraft.position.copy(position)

      // 计算朝向
      const nextPosition = this.getPositionOnPath(waypoints, Math.min(newProgress + 0.01, 1.0))
      if (nextPosition) {
        aircraft.lookAt(nextPosition)
      }
    }

    // 如果到达终点，重新开始
    if (newProgress >= 1.0) {
      aircraft.userData.progress = 0
    }
  }

  /**
   * 根据进度获取路径上的位置
   */
  getPositionOnPath(waypoints, progress) {
    if (waypoints.length < 2) return null

    const points = waypoints.map(wp => new THREE.Vector3(wp.x, wp.y, wp.z))
    const curve = new THREE.CatmullRomCurve3(points)

    return curve.getPoint(progress)
  }

  /**
   * 删除航线
   */
  removeFlightPath(pathId) {
    // 删除路径网格
    const pathMesh = this.pathMeshes.get(pathId)
    if (pathMesh) {
      this.scene.remove(pathMesh)
      this.pathMeshes.delete(pathId)
    }

    // 删除航点标记组
    const waypointGroup = this.waypoints.get(pathId)
    if (waypointGroup) {
      this.scene.remove(waypointGroup)
      this.waypoints.delete(pathId)
    }

    // 删除飞行器
    this.aircrafts.forEach((aircraft, aircraftId) => {
      if (aircraft.userData.pathId === pathId) {
        this.scene.remove(aircraft)
        this.aircrafts.delete(aircraftId)
      }
    })

    // 删除航线数据
    this.flightPaths.delete(pathId)

    console.log('航线已删除:', pathId)
  }

  /**
   * 开始动画循环
   */
  startAnimation() {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      this.map.render()
    }
    animate()
  }

  /**
   * 停止动画
   */
  stopAnimation() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.stopAnimation()
    
    // 清理所有航线
    this.flightPaths.forEach((_, pathId) => {
      this.removeFlightPath(pathId)
    })
    
    // 移除图层
    if (this.glLayer) {
      this.map.remove(this.glLayer)
    }
    
    // 清理Three.js资源
    if (this.renderer) {
      this.renderer.dispose()
    }
  }
}
