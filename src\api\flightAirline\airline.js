import request from '@/utils/request'

// 查询航线列表
export function listAirlines(query) {
  return request({
    url: '/airline/list',
    method: 'get',
    params: query
  })
}

// 查询航线详细
export function getAirline(airlineId) {
  return request({
    url: '/airline/' + airlineId,
    method: 'get'
  })
}

// 新增航线
export function addAirline(data) {
  return request({
    url: '/airline',
    method: 'post',
    data: data
  })
}

// 修改航线
export function updateAirline(data) {
  return request({
    url: '/airline',
    method: 'put',
    data: data
  })
}

// 删除航线
export function delAirline(airlineId) {
  return request({
    url: '/airline/' + airlineId,
    method: 'delete'
  })
}

// 复制航线
export function copyAirline(airlineId) {
  return request({
    url: '/airline/copy/' + airlineId,
    method: 'post'
  })
}

// 导出航线为WPML格式
export function exportAirlineWPML(airlineId) {
  return request({
    url: '/airline/export/wpml/' + airlineId,
    method: 'get',
    responseType: 'blob'
  })
}

// 导入WPML格式航线
export function importAirlineWPML(data) {
  return request({
    url: '/airline/import/wpml',
    method: 'post',
    data: data
  })
}

// 验证航线
export function validateAirline(data) {
  return request({
    url: '/airline/validate',
    method: 'post',
    data: data
  })
}

// 优化航线路径
export function optimizeAirlinePath(airlineId) {
  return request({
    url: '/airline/optimize/' + airlineId,
    method: 'post'
  })
}

// 计算航线统计信息
export function calculateAirlineStats(data) {
  return request({
    url: '/airline/stats',
    method: 'post',
    data: data
  })
}

// 获取航线集合列表（分页）
export function getWaylines(query) {
  return request({
    url: '/wayline/list',
    method: 'get',
    params: query
  })
}

// 获取航线封面图片
export function getWaylineResource(cover) {
  return request({
    url: `/wayline/resource/${cover}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取航线封面图片URL（带认证）
export function getWaylineImageUrl(cover) {
  if (!cover) {
    return null;
  }
  return getWaylineResource(cover);
}

// 删除航线集合
export function delWayline(waylineId) {
  return request({
    url: '/wayline/' + waylineId,
    method: 'delete'
  })
}

// 收藏/取消收藏航线
export function favoriteWayline(id, favorited) {
  return request({
    url: '/wayline/favorite',
    method: 'put',
    data: {
      id: id,
      favorited: favorited
    }
  })
}

// 获取航线详情
export function getWaylineDetail(waylineId) {
  return request({
    url: '/wayline/get',
    method: 'get',
    params: {
      waylineId: waylineId
    }
  })
}

// 创建航线
export function createWayline(data) {
  return request({
    url: '/wayline/createWayline',
    method: 'post',
    data: data
  })
}

// 查询航点列表
export function listWaypoints(airlineId) {
  return request({
    url: '/airline/' + airlineId + '/waypoints',
    method: 'get'
  })
}

// 新增航点
export function addWaypoint(airlineId, data) {
  return request({
    url: '/airline/' + airlineId + '/waypoints',
    method: 'post',
    data: data
  })
}

// 修改航点
export function updateWaypoint(airlineId, waypointId, data) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/' + waypointId,
    method: 'put',
    data: data
  })
}

// 删除航点
export function delWaypoint(airlineId, waypointId) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/' + waypointId,
    method: 'delete'
  })
}

// 批量更新航点顺序
export function updateWaypointsOrder(airlineId, data) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/order',
    method: 'put',
    data: data
  })
}

// 查询航点动作列表
export function listWaypointActions(airlineId, waypointId) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/' + waypointId + '/actions',
    method: 'get'
  })
}

// 新增航点动作
export function addWaypointAction(airlineId, waypointId, data) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/' + waypointId + '/actions',
    method: 'post',
    data: data
  })
}

// 修改航点动作
export function updateWaypointAction(airlineId, waypointId, actionId, data) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/' + waypointId + '/actions/' + actionId,
    method: 'put',
    data: data
  })
}

// 删除航点动作
export function delWaypointAction(airlineId, waypointId, actionId) {
  return request({
    url: '/airline/' + airlineId + '/waypoints/' + waypointId + '/actions/' + actionId,
    method: 'delete'
  })
}

// 执行航线任务
export function executeAirline(airlineId, deviceSn) {
  return request({
    url: '/airline/execute',
    method: 'post',
    data: {
      airlineId: airlineId,
      deviceSn: deviceSn
    }
  })
}

// 暂停航线任务
export function pauseAirline(taskId) {
  return request({
    url: '/airline/pause/' + taskId,
    method: 'post'
  })
}

// 恢复航线任务
export function resumeAirline(taskId) {
  return request({
    url: '/airline/resume/' + taskId,
    method: 'post'
  })
}

// 停止航线任务
export function stopAirline(taskId) {
  return request({
    url: '/airline/stop/' + taskId,
    method: 'post'
  })
}

// 查询航线任务状态
export function getAirlineTaskStatus(taskId) {
  return request({
    url: '/airline/task/' + taskId + '/status',
    method: 'get'
  })
}

// 查询航线任务历史
export function listAirlineTaskHistory(query) {
  return request({
    url: '/airline/task/history',
    method: 'get',
    params: query
  })
}
