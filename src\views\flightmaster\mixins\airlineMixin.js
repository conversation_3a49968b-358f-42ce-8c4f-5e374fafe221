/**
 * 航线规划Mixin
 * 提供航线规划相关功能，包括WPML格式支持、坐标转换、路径优化等
 */

export default {
  data() {
    return {
      // WPML相关常量
      wpmlVersion: '1.0.0',
      wpmlNamespace: 'http://www.dji.com/wpmz/1.0.0',
      
      // 动作类型映射（参考大疆WPML标准）
      wpmlActionTypes: {
        // 飞行动作
        'takeOff': 1,           // 起飞
        'land': 2,              // 降落
        'returnToHome': 3,      // 返航
        'hover': 4,             // 悬停
        'waypoint': 5,          // 航点飞行
        
        // 云台动作
        'gimbalPitch': 101,     // 云台俯仰
        'gimbalYaw': 102,       // 云台偏航
        'gimbalRoll': 103,      // 云台横滚
        
        // 相机动作
        'photo': 201,           // 拍照
        'videoStart': 202,      // 开始录像
        'videoStop': 203,       // 停止录像
        'zoomIn': 204,          // 变焦放大
        'zoomOut': 205,         // 变焦缩小
        
        // 其他动作
        'wait': 301,            // 等待
        'led': 302,             // LED灯控制
        'speaker': 303          // 扬声器控制
      },
      
      // 飞行模式
      flightModes: {
        'waypoint': 0,          // 航点模式
        'straight': 1,          // 直线模式
        'curve': 2,             // 曲线模式
        'coordinated': 3        // 协调转弯模式
      },
      
      // 高度模式
      heightModes: {
        'relative': 0,          // 相对高度
        'absolute': 1,          // 绝对高度
        'ellipsoid': 2          // 椭球高度
      }
    }
  },
  
  methods: {
    /**
     * 将航线数据转换为WPML格式
     * @param {Object} airline 航线数据
     * @returns {Object} WPML格式数据
     */
    convertToWPML(airline) {
      const wpml = {
        version: this.wpmlVersion,
        xmlns: this.wpmlNamespace,
        mission: {
          waylineId: airline.id,
          waylineName: airline.name,
          waylineType: this.getWaylineType(airline.type),
          autoFlightSpeed: airline.defaultSpeed,
          globalHeight: airline.defaultHeight,
          globalHeightMode: this.heightModes.relative,
          waypoints: airline.waypoints.map((waypoint, index) => this.convertWaypointToWPML(waypoint, index))
        },
        metadata: {
          createTime: airline.createTime,
          updateTime: airline.updateTime,
          description: airline.description || '',
          author: 'Flight-UI',
          version: '1.0'
        }
      }
      
      return wpml
    },
    
    /**
     * 将航点转换为WPML格式
     * @param {Object} waypoint 航点数据
     * @param {Number} index 航点索引
     * @returns {Object} WPML格式航点
     */
    convertWaypointToWPML(waypoint, index) {
      return {
        waypointIndex: index,
        location: {
          longitude: waypoint.longitude,
          latitude: waypoint.latitude,
          height: waypoint.height,
          heightMode: this.heightModes.relative
        },
        waypointSpeed: waypoint.speed,
        waypointHeadingMode: 'followWayline',
        waypointTurnMode: 'coordinateTurn',
        useStraightLine: false,
        actions: waypoint.actions.map(action => this.convertActionToWPML(action))
      }
    },
    
    /**
     * 将动作转换为WPML格式
     * @param {Object} action 动作数据
     * @returns {Object} WPML格式动作
     */
    convertActionToWPML(action) {
      const wpmlAction = {
        actionType: this.wpmlActionTypes[action.type] || 301,
        actionParam: {}
      }
      
      switch (action.type) {
        case 'hover':
          wpmlAction.actionParam.hoverTime = action.params?.duration || 3
          break
        case 'photo':
          wpmlAction.actionParam.shootPhotoMode = 0 // 单拍模式
          break
        case 'videoStart':
          wpmlAction.actionParam.recordingMode = 0 // 开始录像
          break
        case 'videoStop':
          wpmlAction.actionParam.recordingMode = 1 // 停止录像
          break
        case 'gimbal':
          wpmlAction.actionParam.gimbalPitchAngle = action.params?.pitch || -45
          break
        case 'wait':
          wpmlAction.actionParam.waitTime = action.params?.duration || 5
          break
      }
      
      return wpmlAction
    },
    
    /**
     * 从WPML格式转换为航线数据
     * @param {Object} wpml WPML格式数据
     * @returns {Object} 航线数据
     */
    convertFromWPML(wpml) {
      const mission = wpml.mission
      const metadata = wpml.metadata || {}
      
      return {
        id: mission.waylineId || this.generateId(),
        name: mission.waylineName || '导入航线',
        description: metadata.description || '',
        type: this.getAirlineTypeFromWaylineType(mission.waylineType),
        defaultSpeed: mission.autoFlightSpeed || 5,
        defaultHeight: mission.globalHeight || 50,
        waypoints: mission.waypoints.map(wp => this.convertWaypointFromWPML(wp)),
        createTime: metadata.createTime ? new Date(metadata.createTime) : new Date(),
        updateTime: metadata.updateTime ? new Date(metadata.updateTime) : new Date()
      }
    },
    
    /**
     * 从WPML格式转换航点
     * @param {Object} wpmlWaypoint WPML格式航点
     * @returns {Object} 航点数据
     */
    convertWaypointFromWPML(wpmlWaypoint) {
      return {
        id: this.generateId(),
        longitude: wpmlWaypoint.location.longitude,
        latitude: wpmlWaypoint.location.latitude,
        height: wpmlWaypoint.location.height,
        speed: wpmlWaypoint.waypointSpeed,
        actions: wpmlWaypoint.actions ? wpmlWaypoint.actions.map(action => this.convertActionFromWPML(action)) : []
      }
    },
    
    /**
     * 从WPML格式转换动作
     * @param {Object} wpmlAction WPML格式动作
     * @returns {Object} 动作数据
     */
    convertActionFromWPML(wpmlAction) {
      const actionType = this.getActionTypeFromWPML(wpmlAction.actionType)
      const action = {
        type: actionType,
        params: {}
      }
      
      switch (actionType) {
        case 'hover':
          action.params.duration = wpmlAction.actionParam.hoverTime || 3
          break
        case 'gimbal':
          action.params.pitch = wpmlAction.actionParam.gimbalPitchAngle || -45
          break
        case 'wait':
          action.params.duration = wpmlAction.actionParam.waitTime || 5
          break
      }
      
      return action
    },
    
    /**
     * 获取航线类型对应的WPML waylineType
     * @param {String} airlineType 航线类型
     * @returns {Number} WPML waylineType
     */
    getWaylineType(airlineType) {
      const typeMap = {
        'normal': 0,
        'inspection': 1,
        'mapping': 2,
        'rescue': 3
      }
      return typeMap[airlineType] || 0
    },
    
    /**
     * 从WPML waylineType获取航线类型
     * @param {Number} waylineType WPML waylineType
     * @returns {String} 航线类型
     */
    getAirlineTypeFromWaylineType(waylineType) {
      const typeMap = {
        0: 'normal',
        1: 'inspection',
        2: 'mapping',
        3: 'rescue'
      }
      return typeMap[waylineType] || 'normal'
    },
    
    /**
     * 从WPML actionType获取动作类型
     * @param {Number} wpmlActionType WPML动作类型
     * @returns {String} 动作类型
     */
    getActionTypeFromWPML(wpmlActionType) {
      const reverseMap = {}
      Object.keys(this.wpmlActionTypes).forEach(key => {
        reverseMap[this.wpmlActionTypes[key]] = key
      })
      return reverseMap[wpmlActionType] || 'wait'
    },
    
    /**
     * 验证WPML格式数据
     * @param {Object} wpml WPML数据
     * @returns {Object} 验证结果
     */
    validateWPML(wpml) {
      const errors = []
      const warnings = []
      
      // 检查基本结构
      if (!wpml.mission) {
        errors.push('缺少mission节点')
      } else {
        // 检查航线基本信息
        if (!wpml.mission.waylineName) {
          warnings.push('缺少航线名称')
        }
        
        // 检查航点
        if (!wpml.mission.waypoints || !Array.isArray(wpml.mission.waypoints)) {
          errors.push('缺少航点数据')
        } else if (wpml.mission.waypoints.length === 0) {
          warnings.push('航点数量为0')
        } else {
          // 检查每个航点
          wpml.mission.waypoints.forEach((waypoint, index) => {
            if (!waypoint.location) {
              errors.push(`航点${index + 1}缺少位置信息`)
            } else {
              if (typeof waypoint.location.longitude !== 'number') {
                errors.push(`航点${index + 1}经度格式错误`)
              }
              if (typeof waypoint.location.latitude !== 'number') {
                errors.push(`航点${index + 1}纬度格式错误`)
              }
              if (typeof waypoint.location.height !== 'number') {
                errors.push(`航点${index + 1}高度格式错误`)
              }
            }
          })
        }
      }
      
      return {
        valid: errors.length === 0,
        errors,
        warnings
      }
    },
    
    /**
     * 生成唯一ID
     * @returns {String} 唯一ID
     */
    generateId() {
      return 'id_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9)
    }
  }
}
