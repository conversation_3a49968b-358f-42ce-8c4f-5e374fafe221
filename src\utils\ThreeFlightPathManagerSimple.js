import * as THREE from 'three'

/**
 * 简化的3D航点管理器 - 严格按照官方示例
 */
export class ThreeFlightPathManager {
  constructor(map, existingScene = null, existingRenderer = null, existingCamera = null) {
    this.map = map
    this.customCoords = map.customCoords
    
    // 使用现有的Three.js场景
    this.scene = existingScene
    this.camera = existingCamera
    this.renderer = existingRenderer
    this.isInitialized = !!existingScene
    
    // 简化数据结构 - 按照官方示例
    this.waypoints = [] // 存储航点的原始经纬度坐标
    this.convertedData = [] // 存储转换后的坐标
    this.waypointMeshes = [] // 存储航点网格对象
    this.pathMesh = null // 存储路径网格对象
    
    // 固定渲染中心点 - 按照官方示例，必须固定
    this.fixedRenderCenter = [116.52, 39.79]

    // 检查必要的依赖
    if (!this.map || !this.customCoords) {
      throw new Error('地图或坐标转换工具未初始化')
    }

    console.log('简化的3D航点管理器初始化完成')
  }

  /**
   * 创建航线路径 - 兼容原有接口
   */
  createFlightPath(pathId, waypoints, options = {}) {
    console.log('创建航线路径:', pathId, waypoints.length, '个航点')
    
    // 清除现有数据
    this.clearWaypoints()
    
    // 添加所有航点
    waypoints.forEach(wp => {
      this.addWaypoint(wp.longitude, wp.latitude, wp.height || 100)
    })
    
    return { id: pathId, waypoints: this.waypoints }
  }

  /**
   * 添加航点 - 严格按照官方示例模式
   */
  addWaypoint(longitude, latitude, height = 100) {
    // 添加到原始航点数组
    this.waypoints.push({ longitude, latitude, height })
    
    // 如果是第一个航点，设置固定渲染中心点
    if (this.waypoints.length === 1) {
      this.fixedRenderCenter = [longitude, latitude]
      console.log('设置固定渲染中心点:', this.fixedRenderCenter)
    }
    
    // 重新转换所有坐标并创建3D对象
    this.updateWaypoints()
  }

  /**
   * 更新航点显示 - 按照官方示例模式
   */
  updateWaypoints() {
    if (this.waypoints.length === 0 || !this.scene) return

    // 清除现有的3D对象
    this.clearMeshes()

    // 按照官方示例：数据使用转换工具进行转换，这个操作必须要提前执行
    this.customCoords.setCenter(this.fixedRenderCenter)
    const lngLatArray = this.waypoints.map(wp => [wp.longitude, wp.latitude])
    this.convertedData = this.customCoords.lngLatsToCoords(lngLatArray)

    // 创建航点球体 - 按照官方示例模式
    this.createWaypointSpheres()

    // 如果有2个以上航点，创建路径
    if (this.waypoints.length >= 2) {
      this.createFlightPath()
    }

    console.log('航点更新完成，共', this.waypoints.length, '个航点')
  }

  /**
   * 创建航点球体 - 严格按照官方示例
   */
  createWaypointSpheres() {
    // 创建球体几何体和材质
    const geometry = new THREE.SphereGeometry(20, 16, 16)
    const material = new THREE.MeshPhongMaterial({
      color: 0xff0000,
      transparent: true,
      opacity: 0.8
    })

    // 遍历转换后的数据，创建球体
    for (let i = 0; i < this.convertedData.length; i++) {
      const d = this.convertedData[i]
      const waypoint = this.waypoints[i]
      
      // 创建网格对象
      const mesh = new THREE.Mesh(geometry, material)
      // 设置网格对象的位置 - 按照官方示例，位置在init中设置后永不改变
      mesh.position.set(d[0], d[1], waypoint.height)
      
      // 添加到数组和场景
      this.waypointMeshes.push(mesh)
      this.scene.add(mesh)
    }
  }

  /**
   * 创建航线路径
   */
  createFlightPath() {
    if (this.convertedData.length < 2) return

    // 创建路径点
    const points = this.convertedData.map((d, i) => 
      new THREE.Vector3(d[0], d[1], this.waypoints[i].height)
    )
    
    // 创建曲线
    const curve = new THREE.CatmullRomCurve3(points)
    
    // 创建管道几何体
    const tubeGeometry = new THREE.TubeGeometry(curve, 64, 8, 8, false)
    const tubeMaterial = new THREE.MeshPhongMaterial({
      color: 0x00ff00,
      transparent: true,
      opacity: 0.8
    })
    
    // 创建路径网格
    this.pathMesh = new THREE.Mesh(tubeGeometry, tubeMaterial)
    this.scene.add(this.pathMesh)
  }

  /**
   * 清除所有网格对象
   */
  clearMeshes() {
    // 清除航点球体
    this.waypointMeshes.forEach(mesh => {
      this.scene.remove(mesh)
    })
    this.waypointMeshes = []

    // 清除路径
    if (this.pathMesh) {
      this.scene.remove(this.pathMesh)
      this.pathMesh = null
    }
  }

  /**
   * 清除所有航点
   */
  clearWaypoints() {
    this.waypoints = []
    this.convertedData = []
    this.clearMeshes()
  }

  /**
   * 删除航线 - 兼容原有接口
   */
  removeFlightPath(pathId) {
    console.log('删除航线:', pathId)
    this.clearWaypoints()
  }

  /**
   * 创建飞行器 - 兼容原有接口
   */
  createAircraft(aircraftId, pathId, options = {}) {
    console.log('创建飞行器:', aircraftId, '在航线:', pathId)
    // 简化版本暂不实现飞行器动画
    return null
  }

  /**
   * 清理资源
   */
  dispose() {
    this.clearWaypoints()
    console.log('简化3D航点管理器资源清理完成')
  }
}
