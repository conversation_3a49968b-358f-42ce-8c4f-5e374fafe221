<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket重复连接修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.disconnected {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .clear-log {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket重复连接修复测试</h1>
        
        <div class="test-section">
            <h3>连接状态</h3>
            <div id="status" class="status disconnected">未连接</div>
            <div>
                <button id="connectBtn" class="button">连接WebSocket</button>
                <button id="disconnectBtn" class="button" disabled>断开连接</button>
                <button id="multiConnectBtn" class="button">测试多次连接</button>
            </div>
        </div>

        <div class="test-section">
            <h3>连接统计</h3>
            <div>
                <p>连接尝试次数: <span id="connectAttempts">0</span></p>
                <p>成功连接次数: <span id="successConnections">0</span></p>
                <p>重连次数: <span id="reconnectCount">0</span></p>
                <p>重复连接阻止次数: <span id="duplicateBlocked">0</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>日志</h3>
            <button class="clear-log" onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // 模拟WebSocket服务
        class MockWebSocketService {
            constructor() {
                this.socket = null;
                this.messageHandler = null;
                this.reconnectCallbacks = [];
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectInterval = 3000;
                this.status = 'disconnected';
            }

            init(username) {
                this.logMessage(`尝试初始化WebSocket连接...`);
                
                // 防止重复初始化
                if (this.socket && (this.socket.readyState === WebSocket.CONNECTING || this.socket.readyState === WebSocket.OPEN)) {
                    this.logMessage("WebSocket已存在且状态正常，跳过重复初始化");
                    updateStats('duplicateBlocked');
                    return;
                }

                // 如果存在旧连接，先关闭
                if (this.socket) {
                    this.logMessage("关闭旧的WebSocket连接");
                    this.socket.close();
                }

                updateStats('connectAttempts');

                try {
                    // 模拟WebSocket连接（使用echo服务器进行测试）
                    const wsUrl = 'wss://echo.websocket.org/';
                    this.logMessage(`WebSocket连接地址: ${wsUrl}`);

                    this.socket = new WebSocket(wsUrl);
                    this.socket.onopen = this.open.bind(this);
                    this.socket.onerror = this.error.bind(this);
                    this.socket.onmessage = this.messageHandler;
                    this.socket.onclose = this.onClose.bind(this);
                    
                    this.status = 'connecting';
                    updateStatus('connecting', '连接中...');
                } catch (error) {
                    this.logMessage(`WebSocket初始化失败: ${error.message}`);
                    this.status = 'error';
                    updateStatus('error', `连接失败: ${error.message}`);
                }
            }

            open() {
                this.logMessage("WebSocket连接成功");
                const isReconnect = this.reconnectAttempts > 0;
                
                if (isReconnect) {
                    updateStats('reconnectCount');
                } else {
                    updateStats('successConnections');
                }
                
                this.reconnectAttempts = 0;
                this.status = 'connected';
                updateStatus('connected', 'WebSocket已连接');

                // 恢复消息处理器
                if (this.messageHandler) {
                    this.socket.onmessage = this.messageHandler;
                    if (isReconnect) {
                        this.logMessage("WebSocket重连成功，消息处理器已恢复");
                    }
                }

                // 只在重连时执行重连回调，避免初次连接时的重复消息
                if (isReconnect) {
                    this.reconnectCallbacks.forEach(callback => {
                        try {
                            callback();
                        } catch (error) {
                            this.logMessage(`执行重连回调失败: ${error.message}`);
                        }
                    });
                }
            }

            error(error) {
                this.logMessage(`WebSocket连接错误: ${error}`);
                this.status = 'error';
                updateStatus('error', '连接错误');
                this.reconnect();
            }

            onClose(event) {
                this.logMessage(`WebSocket连接关闭: ${event.code} ${event.reason}`);
                this.status = 'disconnected';
                updateStatus('disconnected', '连接已断开');
                
                if (event.code !== 1000) { // 非正常关闭
                    this.reconnect();
                }
            }

            reconnect() {
                if (this.maxReconnectAttempts == -1 || this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    this.logMessage(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    setTimeout(() => {
                        this.init("warning-all");
                    }, this.reconnectInterval);
                } else {
                    this.logMessage("WebSocket重连失败，已达到最大重连次数");
                }
            }

            addReconnectCallback(callback) {
                if (typeof callback === 'function') {
                    this.reconnectCallbacks.push(callback);
                    this.logMessage("添加重连回调");
                }
            }

            removeReconnectCallback(callback) {
                const index = this.reconnectCallbacks.indexOf(callback);
                if (index > -1) {
                    this.reconnectCallbacks.splice(index, 1);
                    this.logMessage("移除重连回调");
                }
            }

            close() {
                if (this.socket) {
                    this.socket.close();
                    this.logMessage("手动关闭WebSocket连接");
                }
            }

            logMessage(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('log');
                logElement.textContent += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        // 模拟WebSocket Mixin
        class MockWebSocketMixin {
            constructor() {
                this.websocketStatus = 'disconnected';
                this.websocketError = null;
                this.reconnectCallback = null;
                this.socketService = new MockWebSocketService();
            }

            async connectWebSocket() {
                // 防止重复连接
                if (this.websocketStatus === 'connected' || this.websocketStatus === 'connecting') {
                    this.socketService.logMessage("WebSocket已连接或正在连接中，跳过重复连接");
                    updateStats('duplicateBlocked');
                    return;
                }

                this.socketService.logMessage("开始连接WebSocket...");
                this.websocketStatus = 'connecting';
                this.websocketError = null;

                try {
                    // 清理之前的重连回调（防止重复添加）
                    if (this.reconnectCallback) {
                        this.socketService.removeReconnectCallback(this.reconnectCallback);
                    }

                    // 添加重连回调
                    this.reconnectCallback = () => {
                        this.socketService.logMessage("WebSocket重连成功，更新状态");
                        this.websocketStatus = 'connected';
                        this.websocketError = null;
                        this.showMessage("WebSocket重连成功");
                    };
                    this.socketService.addReconnectCallback(this.reconnectCallback);

                    // 初始化WebSocket连接
                    this.socketService.init("warning-all");

                    // 等待连接建立
                    await this.waitForConnection();

                    this.websocketStatus = 'connected';
                    this.socketService.logMessage("WebSocket连接和消息处理器设置完成");
                    this.showMessage("WebSocket连接成功");
                } catch (error) {
                    this.socketService.logMessage(`WebSocket连接失败: ${error.message}`);
                    this.websocketStatus = 'error';
                    this.websocketError = error.message;
                    this.showMessage(`WebSocket连接失败: ${error.message}`);
                }
            }

            waitForConnection() {
                return new Promise((resolve, reject) => {
                    const checkConnection = () => {
                        if (this.socketService.socket) {
                            if (this.socketService.socket.readyState === WebSocket.OPEN) {
                                resolve();
                            } else if (this.socketService.socket.readyState === WebSocket.CLOSED ||
                                      this.socketService.socket.readyState === WebSocket.CLOSING) {
                                reject(new Error("WebSocket连接失败"));
                            } else {
                                setTimeout(checkConnection, 100);
                            }
                        } else {
                            setTimeout(checkConnection, 100);
                        }
                    };
                    checkConnection();

                    setTimeout(() => {
                        reject(new Error("WebSocket连接超时"));
                    }, 10000);
                });
            }

            disconnect() {
                this.socketService.close();
                this.websocketStatus = 'disconnected';
                updateStatus('disconnected', '已断开连接');
            }

            showMessage(message) {
                // 模拟Vue的$message
                console.log(`Message: ${message}`);
            }
        }

        // 全局变量
        let mockMixin = new MockWebSocketMixin();
        let stats = {
            connectAttempts: 0,
            successConnections: 0,
            reconnectCount: 0,
            duplicateBlocked: 0
        };

        // 更新统计
        function updateStats(type) {
            stats[type]++;
            document.getElementById(type).textContent = stats[type];
        }

        // 更新状态显示
        function updateStatus(status, message) {
            const statusElement = document.getElementById('status');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
            
            // 更新按钮状态
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (status === 'connected') {
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 事件监听
        document.getElementById('connectBtn').addEventListener('click', () => {
            mockMixin.connectWebSocket();
        });

        document.getElementById('disconnectBtn').addEventListener('click', () => {
            mockMixin.disconnect();
        });

        document.getElementById('multiConnectBtn').addEventListener('click', () => {
            // 测试多次快速连接
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    mockMixin.connectWebSocket();
                }, i * 100);
            }
        });

        // 初始化
        updateStatus('disconnected', '未连接');
    </script>
</body>
</html>
